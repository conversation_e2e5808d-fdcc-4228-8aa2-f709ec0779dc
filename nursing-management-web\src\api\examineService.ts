/*
 * FilePath     : \src\api\examineService.ts
 * Author       : 来江禹
 * Date         : 2024-04-24 16:10
 * LastEditors  : 苏军志
 * LastEditTime : 2025-06-26 19:51
 * Description  : 考核API
 * CodeIterationRecord:
 */
import http from "@/utils/http";
import qs from "qs";
export class examineService {
  private static getQuestionBankListApi: string = "Examine/GetQuestionBankList";
  private static saveQuestionBankApi: string = "Examine/SaveQuestionBank";
  private static deleteQuestionBankApi: string = "Examine/DeleteQuestionBank";
  private static importQuestionsApi: string = "Examine/ImportQuestions";
  private static getQuestionsByBankIDApi: string = "Examine/GetQuestionsByBankID";
  private static getQuestionBankDictApi: string = "Examine/GetQuestionBankDict";
  private static getQuestionBankSelectListApi: string = "Examine/GetQuestionBankSelectList";
  private static saveQuestionDataApi: string = "Examine/SaveQuestionData";
  private static deleteQuestionApi: string = "Examine/DeleteQuestion";
  private static getQuestionOptionListApi: string = "Examine/GetQuestionOptionList";
  private static deleteExaminationPaperMainDataApi: string = "Examine/DeleteExaminationPaperMainData";
  private static saveExaminationPaperApi: string = "Examine/SaveExaminationPaper";
  private static getExaminationPaperMainListApi: string = "Examine/GetExaminationPaperMainList";
  private static getPaperFormTemplateApi: string = "Examine/GetPaperFormTemplate";
  private static getExaminationRecordListApi: string = "Examine/GetExaminationRecordList";
  private static deleteExaminationRecordDataApi: string = "Examine/DeleteExaminationRecordData";
  private static saveExaminationRecordDataApi: string = "Examine/SaveExaminationRecordData";
  private static getPaperMainSelectListApi: string = "Examine/GetPaperMainSelectList";
  private static getPaperRuleSelectListApi: string = "Examine/GetPaperRuleSelectList";
  private static publishExamineAPi: string = "Examine/PublishExamine";
  private static getExamineMainListApi: string = "Examine/GetExamineMainList";
  private static getExaminationPlanSummaryListApi: string = "Examine/GetExaminationPlanSummaryList";
  private static changeExamineMainStatusApi: string = "Examine/ChangeExamineMainStatus";
  private static getRetakeEmployeeIDsByRecordIDApi: string = "Examine/GetRetakeEmployeeIDsByRecordID";
  private static saveRetakeExamineApi: string = "Examine/SaveRetakeExamine";
  private static getQuestionComponentsApi: string = "Examine/GetQuestionComponents";
  private static savePracticalExamineApi: string = "Examine/SavePracticalExamine";
  private static getQuestionBankCascaderApi: string = "Examine/GetQuestionBankCascader";
  private static savePracticeExaminationApi: string = "Examine/SavePracticeExamination";
  private static getQuestionTypeCountApi: string = "Examine/GetQuestionTypeCount";
  private static copyExamPaperMainApi: string = "Examine/CopyExamPaperMain";
  private static copyQuestionBankApi: string = "Examine/CopyQuestionBank";
  private static stopPublishExamineApi: string = "Examine/StopPublishExamine";
  private static updateBankSortApi: string = "Examine/UpdateBankSort";
  private static updateQuestionSortApi: string = "Examine/UpdateQuestionSort";
  private static cloneQuestionBankApi: string = "Examine/CloneQuestionBank";
  private static getPaperTemplateByBankIDApi: string = "Examine/GetPaperTemplateByBankID";
  private static getTheoryPaperTemplateByBankIDApi: string = "Examine/GetTheoryPaperTemplateByBankID";
  private static getNotFinishedPracticalRecordsApi: string = "Examine/GetNotFinishedPracticalRecords";
  /**
   * @description:  获取考核题库数据
   * @param params
   * @return
   */
  public static getQuestionBankList(params?: any) {
    return http.get(this.getQuestionBankListApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 保存培训考核题库数据
   * @param params
   * @return
   */
  public static saveQuestionBank(params?: any) {
    return http.post(this.saveQuestionBankApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 删除培训考核题库数据
   * @param params
   * @return
   */
  public static deleteQuestionBank(params?: any) {
    return http.post(this.deleteQuestionBankApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  /**
   * @description: 导入培训考核题目
   * @param params
   * @return
   */
  public static importQuestions(params?: any) {
    return http.post(this.importQuestionsApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 获取考核题库题目数据
   * @param params
   * @returns
   */
  public static getQuestionsByBankID(params?: any) {
    return http.get(this.getQuestionsByBankIDApi, params, { loadingText: Loading.LOAD });
  }

  /**
   * @description:  获取考核题库数据key-value
   * @param params
   * @return
   */
  public static getQuestionBankDict(params?: any) {
    return http.get(this.getQuestionBankDictApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description:  保存题目和题目明细数据
   * @param params
   * @return
   */
  public static saveQuestionData(params?: any) {
    return http.post(this.saveQuestionDataApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description:  删除指定题目
   * @param params
   * @return
   */
  public static deleteQuestion(params?: any) {
    return http.post(this.deleteQuestionApi, qs.stringify(params), { loadingText: Loading.SAVE });
  }
  /**
   * @description:  根据题库ID集合获取题目下拉框数据
   * @param params
   * @return
   */
  public static getQuestionOptionList(params?: any) {
    return http.post(this.getQuestionOptionListApi, qs.stringify(params), { loadingText: Loading.LOAD });
  }
  /**
   * @description: 删除试卷相关数据
   * @param params
   * @return
   */
  public static deleteExaminationPaperMainData(params?: any) {
    return http.post(this.deleteExaminationPaperMainDataApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  /**
   * @description: 保存考核试卷
   * @param params
   * @return
   */
  public static saveExaminationPaper(params?: any) {
    return http.post(this.saveExaminationPaperApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description:  获取试卷主记录数据
   * @param params
   * @return
   */
  public static getExaminationPaperMainList(params?: any) {
    return http.get(this.getExaminationPaperMainListApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description:  获取试卷模版数据
   * @param params
   * @return
   */
  public static getPaperFormTemplate(params?: any) {
    return http.get(this.getPaperFormTemplateApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description:  获取考核主记录数据
   * @param params
   * @return
   */
  public static getExaminationRecordList(params?: any) {
    return http.get(this.getExaminationRecordListApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description:  删除考核主记录数据
   * @param params
   * @return
   */
  public static deleteExaminationRecordData(params?: any) {
    return http.post(this.deleteExaminationRecordDataApi, qs.stringify(params), { loadingText: Loading.LOAD });
  }
  /**
   * @description:  保存考核主记录数据
   * @param params
   * @return
   */
  public static saveExaminationRecordData(params?: any) {
    return http.post(this.saveExaminationRecordDataApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description:  获取试卷下拉框数据
   * @param params
   * @return
   */
  public static getPaperMainSelectList(params?: any) {
    return http.get(this.getPaperMainSelectListApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description:  获取组卷规则下拉框数据
   * @param params
   * @return
   */
  public static getPaperRuleSelectList(params?: any) {
    return http.get(this.getPaperRuleSelectListApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description:  发布考核
   * @param params
   * @return
   */
  public static publishExamine(params?: any) {
    return http.post(this.publishExamineAPi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description:  获取考核主表数据
   * @param params
   * @return
   */
  public static getExamineMainList(params?: any) {
    return http.get(this.getExamineMainListApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description:  获取考核计划汇总数据
   * @param params
   * @return
   */
  public static getExaminationPlanSummaryList(params?: any) {
    return http.get(this.getExaminationPlanSummaryListApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description:  更改考核主表状态
   * @param params
   * @return
   */
  public static changeExamineMainStatus(params?: any) {
    return http.post(this.changeExamineMainStatusApi, qs.stringify(params), { loadingText: Loading.SAVE });
  }
  /**
   * @description:  获取考核计划符合补考的人员
   * @param params
   * @return
   */
  public static getRetakeEmployeeIDsByRecordID(params?: any) {
    return http.get(this.getRetakeEmployeeIDsByRecordIDApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description:  人员考核记录补考
   * @param params
   * @return
   */
  public static saveRetakeExamine(params?: any) {
    return http.post(this.saveRetakeExamineApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description:  获取实操题组件数据
   * @param params
   * @return
   */
  public static getQuestionComponents(params?: any) {
    return http.get(this.getQuestionComponentsApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description:  保存实操类考核
   * @param params
   * @return
   */
  public static savePracticalExamine(params?: any) {
    return http.post(this.savePracticalExamineApi, params, { loadingText: Loading.SAVE });
  }

  /**
   * @description:  获取考核题库级联数据
   * @param params
   * @return
   */
  public static getQuestionBankCascader(params?: any) {
    return http.get(this.getQuestionBankCascaderApi, params, { loadingText: Loading.LOAD });
  }

  /**
   * @description:  获取考核题库下拉框内容
   * @param params
   * @return
   */
  public static getQuestionBankSelectList(params?: any) {
    return http.get(this.getQuestionBankSelectListApi, params, { loadingText: Loading.LOAD });
  }

  /**
   * @description:  保存刷题练习考核记录
   * @param params
   * @return
   */
  public static savePracticeExamination(params?: any) {
    return http.post(this.savePracticeExaminationApi, qs.stringify(params), { loadingText: Loading.LOAD });
  }

  /**
   * @description:  获取考核题目类型统计数据
   * @param params
   * @return
   */
  public static getQuestionTypeCount(params?: any) {
    return http.get(this.getQuestionTypeCountApi, params, {
      loadingText: Loading.LOAD,
      paramsSerializer: (param: any) => {
        return qs.stringify(param, { arrayFormat: "repeat" });
      }
    });
  }

  /**
   * @description:  复制试卷逻辑
   * @param params
   * @return
   */
  public static copyExamPaperMain(params?: any) {
    return http.post(this.copyExamPaperMainApi, qs.stringify(params), { loadingText: Loading.SAVE });
  }
  /**
   * @description:  复制题库逻辑
   * @param params
   * @return
   */
  public static copyQuestionBank(params?: any) {
    return http.post(this.copyQuestionBankApi, qs.stringify(params), { loadingText: Loading.SAVE });
  }
  /**
   * @description:  停止发布考核
   * @param params
   * @return
   */
  public static stopPublishExamine(params: any) {
    return http.post(this.stopPublishExamineApi, qs.stringify(params), { loadingText: Loading.SAVE });
  }
  /**
   * @description:  更新考核题库排序数据
   * @param params 考核题库排序数据
   * @return
   */
  public static updateBankSort(params: any) {
    return http.post(this.updateBankSortApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description:  更新考核题目排序数据
   * @param params 考核题目排序数据
   * @return
   */
  public static updateQuestionSort(params: any) {
    return http.post(this.updateQuestionSortApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description:  根据考核题库ID获取试卷模版数据
   * @param params
   * @return
   */
  public static getPaperTemplateByBankID(params: any) {
    return http.get(this.getPaperTemplateByBankIDApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description:  复制题库
   * @param params
   * @return
   */
  public static cloneQuestionBank(params?: any) {
    return http.post(this.cloneQuestionBankApi, qs.stringify(params), { loadingText: Loading.SAVE });
  }
  /**
   * @description:  获取理论题库题目模板数据
   * @param params
   * @returns
   */
  public static getTheoryPaperTemplateByBankID(params: any) {
    return http.get(this.getTheoryPaperTemplateByBankIDApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description:  获取未完成的的实操类考核计划记录
   * @param params
   * @returns
   */
  public static getNotFinishedPracticalRecords(params?: any) {
    return http.get(this.getNotFinishedPracticalRecordsApi, params, { loadingText: Loading.LOAD });
  }
}
