<!--
 * FilePath     : \src\views\examineManagement\examinationMain.vue
 * Author       : 来江禹
 * Date         : 2024-08-24 08:10
 * LastEditors  : 张现忠
 * LastEditTime : 2025-07-10 16:41
 * Description  : 考核计划汇总页面
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="examination-plan-summary" showFooter :drawerOptions="drawerOptions">
    <template #header>
      日期：
      <el-date-picker
        v-model="filterDate"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        type="daterange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        class="header-date"
        @change="getPlanSummaryList()"
      />
      <examination-type-radio v-model="examinationType" type="Examination" @change="getPlanSummaryList"></examination-type-radio>
      <span>
        监考人：
        <employee-selector label="" v-model="examineEmployeeID" :filterable="true" :clearable="true" @change="getPlanSummaryList()" />
      </span>
      <export-excel class="export-excel" :exportExcelOption="exportExcelOption">
        <el-button class="print-button" @click="createExportExcelParam">导出考核计划</el-button>
      </export-excel>
    </template>
    <el-table
      ref="planSummaryTableRef"
      :data="planSummaryList"
      border
      stripe
      height="100%"
      row-key="groupID"
      :span-method="tableRowSpanMethod"
    >
      <el-table-column :min-width="convertPX(160)" label="考核名称" prop="examineName" align="left">
        <template #default="{ row }">
          <span>{{ getDisplayExamineName(row) }}</span>
        </template>
      </el-table-column>
      <el-table-column :width="convertPX(120)" label="考核级别" prop="examinationLevelName" align="center"></el-table-column>
      <el-table-column :width="convertPX(100)" label="监考人" prop="examinerName" align="center"></el-table-column>
      <el-table-column :width="convertPX(100)" label="应参加人数" prop="shouldParticipateCount" align="center"></el-table-column>
      <el-table-column :width="convertPX(100)" label="参加人数" prop="participateCount" align="center"></el-table-column>
      <el-table-column :width="convertPX(100)" label="未参加人数" prop="notParticipateCount" align="center"></el-table-column>
      <el-table-column :width="convertPX(120)" label="及格人数/达标人数" prop="passCount" align="center">
        <template #default="{ row }">
          <span>{{ row.passCount }}</span>
        </template>
      </el-table-column>
      <el-table-column :width="convertPX(100)" label="参考率(%)" prop="participationRate" align="center">
        <template #default="{ row }">
          <span>{{ row.participationRate }}%</span>
        </template>
      </el-table-column>
      <el-table-column :width="convertPX(100)" label="合格率(%)" prop="passRate" align="center">
        <template #default="{ row }">
          <span>{{ row.passRate }}%</span>
        </template>
      </el-table-column>
      <el-table-column :width="convertPX(120)" label="集中考核参加率(%)" prop="centralExamParticipationRate" align="center">
        <template #default="{ row }">
          <span>{{ row.centralExamParticipationRate }}%</span>
        </template>
      </el-table-column>
      <el-table-column :width="convertPX(120)" label="首次考核合格率(%)" prop="firstExamPassRate" align="center">
        <template #default="{ row }">
          <span>{{ row.firstExamPassRate }}%</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="convertPX(120)" align="center" fixed="right">
        <template #default="{ row }">
          <div class="operation-wrapper">
            <el-tooltip content="批量补考" placement="top">
              <i class="iconfont icon-supplement" v-permission:B="1" @click="openBatchRetakeDialog(row)"></i>
            </el-tooltip>
            <el-tooltip content="查看详情" placement="top">
              <i class="iconfont icon-info" @click="openDetailDrawer(row)"></i>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-pagination
        small
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[25, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tableDataCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </template>
    <template #drawerContent>
      <examination-main-detail
        v-if="drawerOptions.showDrawer"
        :examineRecordID="selectedPlanRecord?.examinationRecordID"
        :examinationType="selectedPlanRecord?.examinationType"
      />
    </template>
  </base-layout>
</template>
<script setup lang="ts">
// #region 引入
import ExaminationMainDetail from "./examinationMainDetail.vue";
let { userStore } = useStore();
const convertPX: any = inject("convertPX");
const { setTableRowSpanArr, tableRowSpanMethod } = useTable();
// #endregion

const props = defineProps({
  examineRecordID: {
    type: String,
    default: undefined
  },
  examinationType: {
    type: String,
    default: ""
  }
});

// #region 定义变量
const examinationType = ref<string>(props.examinationType || "1");
const filterDate = ref<string[]>([datetimeUtil.getMonthFirstDay(), datetimeUtil.getMonthLastDay()]);
const examineEmployeeID = ref<string>(userStore.employeeID);
const pageSize = ref<number>(30);
const tableDataCount = ref<number>(0);
const currentPage = ref<number>(1);
const planSummaryTableRef = ref<any>();
const planSummaryList = ref<Record<string, any>[]>([]);
const allPlanSummaryList = ref<Record<string, any>[]>([]);
const selectedPlanRecord = ref<Record<string, any> | null>(null);
// #endregion

// #region 初始化
onMounted(() => {
  getPlanSummaryList();
});
// #endregion

// #region 抽屉配置
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "考核记录详情",
  showDrawer: false,
  drawerSize: "90%",
  showConfirm: false,
  showCancel: false
});
// #endregion

// #region 业务逻辑
/**
 * @description: 获取考核计划汇总数据
 * @return
 */
const getPlanSummaryList = () => {
  const params = {
    examineEmployeeID: examineEmployeeID.value,
    startDate: filterDate.value[0],
    endDate: filterDate.value[1],
    examinationType: examinationType.value
  };

  // 暂时使用模拟数据，后续替换为真实API调用
  // examineService.getExaminationPlanSummaryList(params).then((res: any) => {
  //   const data = res ?? [];
  //   processPlanSummaryData(data);
  // });

  console.log("查询参数:", params); // 临时日志，便于调试

  // 模拟数据
  const mockData = [
    {
      groupID: "group1",
      examinationRecordID: "record1",
      examineName: "护理技能考核",
      examinationLevelName: "初级",
      examinerName: "张医生",
      shouldParticipateCount: 50,
      participateCount: 48,
      notParticipateCount: 2,
      passCount: 45,
      participationRate: 96.0,
      passRate: 93.8,
      centralExamParticipationRate: 85.4,
      firstExamPassRate: 87.5,
      examinationType: "1",
      batchNumber: 1
    },
    {
      groupID: "group1",
      examinationRecordID: "record2",
      examineName: "护理技能考核",
      examinationLevelName: "初级",
      examinerName: "张医生",
      shouldParticipateCount: 30,
      participateCount: 28,
      notParticipateCount: 2,
      passCount: 26,
      participationRate: 93.3,
      passRate: 92.9,
      centralExamParticipationRate: 82.1,
      firstExamPassRate: 85.7,
      examinationType: "1",
      batchNumber: 2
    },
    {
      groupID: "group2",
      examinationRecordID: "record3",
      examineName: "理论知识考试",
      examinationLevelName: "中级",
      examinerName: "李医生",
      shouldParticipateCount: 40,
      participateCount: 38,
      notParticipateCount: 2,
      passCount: 35,
      participationRate: 95.0,
      passRate: 92.1,
      centralExamParticipationRate: 89.5,
      firstExamPassRate: 86.8,
      examinationType: "1",
      batchNumber: 1
    }
  ];

  processPlanSummaryData(mockData);
};

/**
 * @description: 处理考核计划汇总数据
 * @param data 原始数据
 */
const processPlanSummaryData = (data: Record<string, any>[]) => {
  allPlanSummaryList.value = data;

  // 设置表格行合并
  setTableRowSpanArr(data, ["groupID"]);

  // 分页处理
  tableDataCount.value = data.length;
  planSummaryList.value = currentChangePage();
};

/**
 * @description: 获取显示的考核名称（包含分批标识）
 * @param row 行数据
 * @return 显示名称
 */
const getDisplayExamineName = (row: Record<string, any>) => {
  if (row.batchNumber && row.batchNumber > 1) {
    return `${row.examineName}(第${row.batchNumber}批)`;
  }
  return row.examineName;
};

/**
 * @description: 打开批量补考对话框（预留功能）
 * @param row 行数据
 */
const openBatchRetakeDialog = (row: Record<string, any>) => {
  // 预留功能，暂时显示提示信息
  console.log("批量补考行数据:", row);
  showMessage("info", "批量补考功能开发中...");
};

/**
 * @description: 打开详情抽屉
 * @param row 行数据
 */
const openDetailDrawer = (row: Record<string, any>) => {
  selectedPlanRecord.value = row;
  drawerOptions.value.showDrawer = true;
};
// #endregion

// #region 导出excel相关逻辑
const exportExcelOption = ref<ExportExcelView[]>([]);
// 导出Excel列配置
const exportExcelColumns = reactive({
  examineName: "考核名称",
  examinationLevelName: "考核级别",
  examinerName: "监考人",
  shouldParticipateCount: "应参加人数",
  participateCount: "参加人数",
  notParticipateCount: "未参加人数",
  passCount: "及格人数/达标人数",
  participationRate: "参考率(%)",
  passRate: "合格率(%)",
  centralExamParticipationRate: "集中考核参加率(%)",
  firstExamPassRate: "首次考核合格率(%)"
});
/**
 * @description: 创建导出Excel参数
 */
const createExportExcelParam = () => {
  exportExcelOption.value = [];
  let cloneData = common.clone(allPlanSummaryList.value);
  cloneData.forEach((item: any) => {
    item.examineName = getDisplayExamineName(item);
  });
  exportExcelOption.value.push({
    buttonName: "导出数据",
    fileName: `考核计划汇总${datetimeUtil.getNow("yyyyMMddhhmm")}`,
    sheetName: "考核计划汇总",
    columnData: exportExcelColumns,
    tableData: cloneData
  });
};
// #endregion

// #region 分页逻辑
/**
 * @description: 切换页数配置
 * @param val
 * @return
 */
const handleSizeChange = (val: any) => {
  pageSize.value = val;
  planSummaryList.value = currentChangePage();
};
/**
 * @description: 切页
 * @param val
 * @return
 */
const handleCurrentChange = (val: any) => {
  currentPage.value = val;
  planSummaryList.value = currentChangePage();
};
/**
 * @description: 分页
 * @param size
 * @param current
 * @return
 */
const currentChangePage = () => {
  return allPlanSummaryList.value.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value);
};
// #endregion
</script>
<style lang="scss">
.examination-plan-summary {
  .header-date {
    width: 300px;
    margin-right: 20px;
  }
  .export-excel {
    float: right;
  }

  // 操作列样式
  .operation-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;

    .iconfont {
      font-size: 16px;
      margin: 0;
      padding: 4px;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(64, 158, 255, 0.1);
        transform: scale(1.1);
      }

      &.icon-supplement {
        color: #ff7400;
        &:hover {
          background-color: rgba(255, 116, 0, 0.1);
        }
      }

      &.icon-info {
        color: #409eff;
        &:hover {
          background-color: rgba(64, 158, 255, 0.1);
        }
      }
    }
  }
}
</style>
