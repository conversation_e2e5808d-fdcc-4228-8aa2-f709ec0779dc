<!--
 * FilePath     : \src\views\examineManagement\examinationMainDetail.vue
 * Author       : 来江禹
 * Date         : 2024-08-24 08:10
 * LastEditors  : 苏军志
 * LastEditTime : 2025-06-27 12:08
 * Description  : 考核主表数据(人员考核记录)详情页面
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="examination-main" showFooter :drawerOptions="drawerOptions">
    <template #header>
      <template v-if="!examineRecordID">
        日期：
        <el-date-picker
          v-model="filterDate"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          class="header-date"
          @change="getMainList()"
        />
        <examination-type-radio v-model="examinationType" type="Examination" @change="getMainList"></examination-type-radio>
        <span>
          监考人：
          <employee-selector label="" v-model="examineEmployeeID" :filterable="true" :clearable="true" @change="getMainList()" />
        </span>
      </template>
      <export-excel class="export-excel" :exportExcelOption="exportExcelOption">
        <el-button class="print-button" @click="createExportExcelParam">导出考核记录</el-button>
      </export-excel>
    </template>
    <el-table
      ref="examineTableRef"
      :data="examinationMainList"
      @filter-change="handleFilterChange"
      border
      stripe
      height="100%"
      row-key="examinationMainID"
    >
      <el-table-column
        :min-width="convertPX(140)"
        label="考核名称"
        prop="examineName"
        column-key="examinationRecordID"
        :filters="examinationRecordFilters"
        :filter-method="( value: string, row: Record<string, any>)=>filterData(value,row, 'examinationRecordID')"
      ></el-table-column>
      <el-table-column
        :width="convertPX(120)"
        label="考核级别"
        prop="examinationLevelName"
        align="center"
        column-key="examinationLevel"
        :filters="examinationLevelFilters"
        :filter-method="( value: string, row: Record<string, any>)=>filterData(value,row, 'examinationLevel')"
      ></el-table-column>
      <el-table-column :min-width="convertPX(140)" label="部门" prop="departmentName"></el-table-column>
      <el-table-column :width="convertPX(100)" label="考核人" prop="employeeName" align="center"></el-table-column>
      <el-table-column
        :width="convertPX(120)"
        label="考核人层级"
        prop="capabilityLevelName"
        align="center"
        column-key="capabilityLevelID"
        :filters="capabilityLevelFilters"
        :filter-method="( value: string, row: Record<string, any>)=>filterData(value,row, 'capabilityLevelID')"
      ></el-table-column>
      <el-table-column
        label="状态"
        :width="convertPX(110)"
        align="center"
        column-key="statusCode"
        :filters="examinationStatusFilters"
        :filter-method="( value: string, row: Record<string, any>)=>filterData(value,row, 'statusCode')"
      >
        <template v-slot="{ row }">
          <el-tag v-if="row.statusCode" :type="getExamineStatusTag(row.statusCode)">{{ row.statusName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column :width="convertPX(100)" label="监考人" prop="examinerName" align="center"></el-table-column>
      <el-table-column
        :width="convertPX(90)"
        label="分数"
        align="center"
        column-key="score"
        :filters="passingSwitchFilters"
        :filter-method="( value: string, row: Record<string, any>)=>filterData(value,row, 'score')"
      >
        <template #default="{ row }">
          <span :class="row.score < row.passingScore ? 'no-passing' : ''">{{ row.score }}</span>
        </template>
      </el-table-column>
      <el-table-column :min-width="convertPX(80)" label="是否补考" align="center">
        <template #default="{ row }">
          <span v-if="row.retakeFlag">√</span>
        </template>
      </el-table-column>
      <el-table-column :min-width="convertPX(80)" label="补考分数" align="center">
        <template #default="{ row }">
          <span :class="row.retakeScore < row.passingScore ? 'no-passing' : ''">{{ row.retakeScore }}</span>
        </template>
      </el-table-column>
      <el-table-column :min-width="convertPX(80)" label="作弊次数" prop="cheatedCount" align="center"></el-table-column>
      <el-table-column :width="convertPX(160)" label="开始时间" prop="startDateTime" align="center">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.startDateTime, type: 'datetime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <el-table-column :width="convertPX(160)" label="结束时间" prop="endDateTime" align="center">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.endDateTime, type: 'datetime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="convertPX(60)" align="center">
        <template #default="{ row }">
          <!-- 非补考显示 -->
          <template v-if="!row.retakeFlag">
            <!-- 再考核时间内且非正常交卷才能恢复考核 -->
            <!-- <el-tooltip content="恢复考试">
              <i
                class="iconfont icon-delay"
                v-permission:B="1"
                v-visibilityHidden="row.statusCode == '5' && Number(getTimeDifference(row.examineEndDateTime, getNow(), 'date', 'S')) < 0"
                @click="recoverExamine(row.examinationMainID)"
              ></i>
            </el-tooltip> -->
            <el-tooltip content="设置补考">
              <!-- 已交卷 或者（待考核状态且当前时间大于考核计划结束时间） -->
              <i
                class="iconfont icon-supplement"
                v-permission:B="1"
                v-visibilityHidden="
                  ['4', '5', '6'].includes(row.statusCode) ||
                  (row.statusCode == 1 && Number(getTimeDifference(row.examineEndDateTime, getNow(), 'date', 'S')) > 0)
                "
                @click="setRetakeExamine(row)"
              ></i>
            </el-tooltip>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-pagination
        small
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[25, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tableDataCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </template>
    <template #drawerContent>
      <el-form :model="retakeExamine" ref="retakeExamineFormRef" label-width="80" :rules="rules">
        <el-form-item label="开始时间：" prop="startDateTime">
          <el-date-picker
            class="drawer-select"
            v-model="retakeExamine.startDateTime"
            type="datetime"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm"
            placeholder="选择开始时间"
            :disabled-date="(date: Date) => disabledDate(date, 'start')"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间：" prop="endDateTime">
          <el-date-picker
            class="drawer-select"
            v-model="retakeExamine.endDateTime"
            type="datetime"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm"
            placeholder="选择结束时间"
            :disabled-date="(date: Date) => disabledDate(date, 'end')"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </template>
  </base-layout>
</template>
<script setup lang="ts">
// #region 引入
let { userStore } = useStore();
let { getTimeDifference, getNow } = useUtils();
const convertPX: any = inject("convertPX");
const { getExamineStatusTag } = useStatusTag();
// #endregion
const props = defineProps({
  examineRecordID: {
    type: String,
    default: undefined
  },
  examinationType: {
    type: String,
    default: ""
  }
});
// #region 定义变量
const examinationType = ref<string>(props.examinationType || "1");
const filterDate = ref<string[]>([datetimeUtil.getMonthFirstDay(), datetimeUtil.getMonthLastDay()]);
const examineEmployeeID = ref<string>(userStore.employeeID);
const pageSize = ref<number>(30);
const tableDataCount = ref<number>(0);
const currentPage = ref<number>(1);
const examineTableRef = ref<any>();
// #endregion

// #region 初始化
onMounted(() => {
  init();
  getMainList();
});
const examinationLevelFilters = ref<Array<Record<any, any>>>([]);
const examinationStatusFilters = ref<Array<Record<any, any>>>([]);
const capabilityLevelFilters = ref<Array<Record<any, any>>>([]);
const passingSwitchFilters = ref<Array<Record<any, any>>>([]);
const init = () => {
  // 获取考核级别字典
  const examinationLevelParams: SettingDictionaryParams = {
    settingType: "ExaminationManagement",
    settingTypeCode: "ExaminationRecord",
    settingTypeValue: "ExaminationLevel"
  };
  settingDictionaryService.getSettingDictionaryDict(examinationLevelParams).then((datas: any) => {
    examinationLevelFilters.value = datas.map((setting: any) => ({
      text: setting.label,
      value: setting.value
    }));
  });
  // 获取考核状态字典
  const statusParams: SettingDictionaryParams = {
    settingType: "ExaminationManagement",
    settingTypeCode: "ExaminationMain",
    settingTypeValue: "StatusCode"
  };
  settingDictionaryService.getSettingDictionaryDict(statusParams).then((datas: any) => {
    examinationStatusFilters.value = datas.map((setting: any) => ({
      text: setting.label,
      value: setting.value
    }));
  });
  // 获取层级字典
  let { getCapabilityLevelData } = useDictionaryData();
  getCapabilityLevelData().then((datas) => {
    capabilityLevelFilters.value = datas.map((setting: any) => ({
      text: setting.label,
      value: setting.value
    }));
  });
  // 获取分数筛选字典
  const params: SettingDictionaryParams = {
    settingType: "ExaminationManagement",
    settingTypeCode: "ExaminationMain",
    settingTypeValue: "PassingSwitch"
  };
  settingDictionaryService.getSettingDictionaryDict(params).then((datas: any) => {
    passingSwitchFilters.value = datas.map((setting: any) => ({
      text: setting.label,
      value: setting.value
    }));
  });
};
// #endregion

// #region 业务逻辑
const examinationMainList = ref<Record<string, any>[]>([]);
const copyExaminationMainList = ref<Record<string, any>[]>([]);
const allExaminationMainList = ref<Record<string, any>[]>([]);
const examinationRecordFilters = ref<Array<Record<any, any>>>([]);
/**
 * @description: 获取考核主表数据
 * @return
 */
const getMainList = () => {
  // 清空表格筛选
  examineTableRef.value?.clearFilter();
  let params = {
    examineEmployeeID: props.examineRecordID ? "" : examineEmployeeID.value,
    examineRecordID: props.examineRecordID ?? "",
    startDate: filterDate.value[0],
    endDate: filterDate.value[1],
    examinationType: examinationType.value
  };
  examineService.getExamineMainList(params).then((res: any) => {
    copyExaminationMainList.value = res ?? [];
    allExaminationMainList.value = res ?? [];
    examinationMainList.value = currentChangePage();
    tableDataCount.value = allExaminationMainList.value.length;
    setExaminationRecordFilters();
  });
};
/**
 * @description: 设置考核计划筛选字典
 */
const setExaminationRecordFilters = () => {
  examinationRecordFilters.value = copyExaminationMainList.value.map((examinationMain: any) => ({
    text: examinationMain.examineName,
    value: examinationMain.examinationRecordID
  }));
  // 去重
  examinationRecordFilters.value = [...new Set(examinationRecordFilters.value.map((item) => JSON.stringify(item)))].map((item) =>
    JSON.parse(item)
  );
};
// /**
//  * @description: 恢复考试
//  * @param examinationMainID
//  * @return
//  */
// const recoverExamine = (examinationMainID: string) => {
//   let params = {
//     examinationMainID: examinationMainID,
//     statusCode: "3"
//   };
//   examineService.changeExamineMainStatus(params).then((res) => {
//     if (res) {
//       getMainList();
//       showMessage("success", "恢复成功！");
//     }
//   });
// };
// #endregion

//#region 补考相关逻辑
const retakeExamine = ref<Record<string, any>>({});
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "设置补考",
  showDrawer: false,
  drawerSize: "30%",
  showConfirm: true,
  confirm: async () => saveRetakeExamine()
});
/**
 * @description: 获取时间选择禁用区间
 * @param date 当前选择的日期
 * @param flag {"start" | "end"} 标识是限制开始时间还是结束时间的方法
 * @return
 */
const disabledDate = (time: Date, type: string) => {
  const date = datetimeUtil.formatDate(time, "yyyy-MM-dd");
  if (type === "start") {
    if (!retakeExamine.value.endDateTime) {
      return date < datetimeUtil.getNowDate("yyyy-MM-dd");
    }
    return date < datetimeUtil.getNowDate("yyyy-MM-dd") || date > datetimeUtil.formatDate(retakeExamine.value.endDateTime, "yyyy-MM-dd");
  }
  if (type === "end") {
    if (!retakeExamine.value.startDateTime) {
      return date < datetimeUtil.getNowDate("yyyy-MM-dd");
    }
    return date < datetimeUtil.formatDate(retakeExamine.value.startDateTime, "yyyy-MM-dd");
  }
  return false;
};
const rules = {
  startDateTime: [{ required: true, message: "请选择补考开始时间", trigger: "change" }],
  endDateTime: [{ required: true, message: "请选择补考结束时间", trigger: "change" }]
};
/**
 * @description: 打开补考设置
 * @param row
 * @return
 */
const setRetakeExamine = (row: any) => {
  retakeExamine.value = {
    examinationRecordID: row.examinationRecordID,
    employeeIDs: [row.employeeID]
  };
  drawerOptions.value.showDrawer = true;
};
const retakeExamineFormRef = shallowRef();
const { validateRule } = useForm();
/**
 * @description: 保存补考
 * @return
 */
const saveRetakeExamine = async () => {
  // 校验表单
  if (!(await validateRule(retakeExamineFormRef))) {
    return false;
  }
  examineService.saveRetakeExamine(retakeExamine.value).then((res) => {
    if (res) {
      showMessage("success", "设置补考成功！");
      drawerOptions.value.showDrawer = false;
      getMainList();
    }
  });
};
// #endregion

// #region 表格列筛选方法
/**
 * @description: 筛选表格数据
 */
const filterData = (
  value: string,
  row: Record<string, any>,
  type: "examinationRecordID" | "examinationLevel" | "capabilityLevelID" | "statusCode" | "score"
) => {
  // 这里只需要返回布尔值，el-table会自动处理筛选
  if (type === "examinationRecordID") {
    return row.examinationRecordID === value;
  }
  if (type === "examinationLevel") {
    return row.examinationLevel === value;
  }
  if (type === "capabilityLevelID") {
    return row.capabilityLevelID === value;
  }
  if (type === "statusCode") {
    return row.statusCode === value;
  }
  if (type === "score") {
    if (value === "0") {
      return (row.score || row.retakeScore) && (row.score >= row.passingScore || row.retakeScore >= row.passingScore);
    }
    if (value === "1") {
      return (row.score || row.retakeScore) && (row.score < row.passingScore || row.retakeScore < row.passingScore);
    }
    if (value === "2") {
      return !row.score && !row.retakeScore;
    }
  }
  return true;
};
const activeFilters = ref<Record<string, any[]>>({});
/**
 * @description: 筛选条件触发逻辑，重置筛选条件，绑定值防空
 * @param filters 表格筛选条件
 * @return
 */
const handleFilterChange = (filters: Record<string, any>) => {
  activeFilters.value = { ...activeFilters.value, ...filters };
  let filteredData = [...copyExaminationMainList.value];
  Object.entries(activeFilters.value).forEach(([key, values]) => {
    if (values && values.length > 0) {
      filteredData = filteredData.filter((row) => {
        if (key === "examinationRecordID") {
          return values.includes(row.examinationRecordID);
        }
        if (key === "examinationLevel") {
          return values.includes(row.examinationLevel);
        }
        if (key === "capabilityLevelID") {
          return values.includes(row.capabilityLevelID);
        }
        if (key === "statusCode") {
          return values.includes(row.statusCode);
        }
        if (key === "score") {
          return values.some((value: any) => {
            if (value === "0") {
              return (row.score || row.retakeScore) && (row.score >= row.passingScore || row.retakeScore >= row.passingScore);
            }
            if (value === "1") {
              return (row.score || row.retakeScore) && (row.score < row.passingScore || row.retakeScore < row.passingScore);
            }
            if (value === "2") {
              return !row.score && !row.retakeScore;
            }
            return true;
          });
        }
        return true;
      });
    }
  });
  allExaminationMainList.value = filteredData;
  tableDataCount.value = allExaminationMainList.value.length;
  currentPage.value = 1;
  examinationMainList.value = currentChangePage();
};
// #endregion

// #region 导出excel相关逻辑
const exportExcelOption = ref<ExportExcelView[]>([]);
// 导出Excel列配置
const exportExcelColumns = reactive({
  examineName: "考核名称",
  examinationLevelName: "考核级别",
  departmentName: "部门",
  employeeName: "考核人",
  capabilityLevelName: "考核人层级",
  statusName: "状态",
  examinerName: "监考人",
  score: "分数",
  retakeFlag: "是否补考",
  retakeScore: "补考分数",
  cheatedCount: "作弊次数",
  startDateTime: "开始时间",
  endDateTime: "结束时间"
});
/**
 * @description: 创建导出Excel参数
 */
const createExportExcelParam = () => {
  exportExcelOption.value = [];
  let cloneData = common.clone(allExaminationMainList.value);
  cloneData.forEach((item: any) => {
    item.retakeFlag = item.retakeFlag ? "√" : "";
    item.startDateTime = item.startDateTime ? datetimeUtil.formatDate(item.startDateTime, "yyyy-MM-dd hh:mm") : "";
    item.endDateTime = item.endDateTime ? datetimeUtil.formatDate(item.endDateTime, "yyyy-MM-dd hh:mm") : "";
    item.addDateTime = item.addDateTime ? datetimeUtil.formatDate(item.addDateTime, "yyyy-MM-dd hh:mm") : "";
    item.modifyDateTime = item.modifyDateTime ? datetimeUtil.formatDate(item.modifyDateTime, "yyyy-MM-dd hh:mm") : "";
  });
  sortByKeys(cloneData, ["statusCode", "capabilityLevelID"], [0, 1]);
  exportExcelOption.value.push({
    buttonName: "导出数据",
    fileName: `考核记录${datetimeUtil.getNow("yyyyMMddhhmm")}`,
    sheetName: "考核记录",
    columnData: exportExcelColumns,
    tableData: cloneData
  });
};
// #endregion

// #region 分页逻辑

/**
 * @description: 切换页数配置
 * @param val
 * @return
 */
const handleSizeChange = (val: any) => {
  pageSize.value = val;
  examinationMainList.value = currentChangePage();
};
/**
 * @description: 切页
 * @param val
 * @return
 */
const handleCurrentChange = (val: any) => {
  currentPage.value = val;
  examinationMainList.value = currentChangePage();
};
/**
 * @description: 分页
 * @param size
 * @param current
 * @return
 */
const currentChangePage = () => {
  return allExaminationMainList.value.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value);
};
// #endregion
</script>
<style lang="scss">
.examination-main {
  .header-date {
    width: 300px;
    margin-right: 20px;
  }
  .export-excel {
    float: right;
  }
  .no-passing {
    color: #ff0000;
  }
}
</style>
